{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "title": "ICOM Statistics Container Schema", "description": "JSON Schema for ICOM statistics data container", "additionalProperties": false, "properties": {"class": {"type": "string", "enum": ["Container"], "description": "Class identifier for the container"}, "cmdKey": {"type": ["string", "null"], "description": "Command key identifier"}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when the data was emitted"}, "entity": {"type": ["string", "null"], "description": "Entity identifier"}, "flow": {"type": ["string", "null"], "description": "Flow path identifier"}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when the data was last modified"}, "name": {"type": "string", "description": "Name identifier for the container"}, "nb": {"type": "integer", "minimum": 0, "description": "Number of objects in the container"}, "pub": {"type": "boolean", "description": "Publication flag"}, "objects": {"type": "array", "description": "Array of statistics objects", "items": {"anyOf": [{"$ref": "#/definitions/StatAlarms"}, {"$ref": "#/definitions/StatBattery"}, {"$ref": "#/definitions/StatBoot"}, {"$ref": "#/definitions/StatCPU"}, {"$ref": "#/definitions/StatCellNeighbour"}, {"$ref": "#/definitions/StatCellServing"}, {"$ref": "#/definitions/StatClock"}, {"$ref": "#/definitions/StatDoor"}, {"$ref": "#/definitions/StatEthernet"}, {"$ref": "#/definitions/StatFS"}, {"$ref": "#/definitions/StatFlash"}, {"$ref": "#/definitions/StatLog"}, {"$ref": "#/definitions/StatMeter"}, {"$ref": "#/definitions/StatNetwork"}, {"$ref": "#/definitions/StatRAM"}, {"$ref": "#/definitions/StatSIM"}, {"$ref": "#/definitions/StatSecurity"}, {"$ref": "#/definitions/StatSupply"}, {"$ref": "#/definitions/StatWAN"}]}}}, "required": ["class", "emitted", "entity", "name", "nb", "objects", "pub"], "definitions": {"BaseStatObject": {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"], "description": "Command key identifier"}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when the data was emitted"}, "entity": {"type": ["string", "null"], "description": "Entity identifier"}, "flow": {"type": ["string", "null"], "description": "Flow path identifier"}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when the data was last modified"}, "name": {"type": "string", "description": "Name identifier for the statistics object"}, "pub": {"type": "boolean", "description": "Publication flag"}}, "required": ["class", "emitted", "entity", "name", "pub"]}, "StatObjectWithDateDuration": {"allOf": [{"$ref": "#/definitions/BaseStatObject"}, {"type": "object", "properties": {"date": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp for the statistics period start"}, "duration": {"type": "integer", "minimum": 0, "description": "Duration of the statistics period in seconds"}}, "required": ["date", "duration"]}]}, "StatAlarms": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatAlarms"]}, "ignored": {"type": "integer", "minimum": 0, "description": "Number of ignored alarms"}, "lost": {"type": "integer", "minimum": 0, "description": "Number of lost alarms"}, "notSent": {"type": "integer", "minimum": 0, "description": "Number of alarms not sent"}}, "required": ["class", "ignored", "lost", "notSent"]}]}, "StatBattery": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatBattery"]}, "batteryAvg": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Average battery level percentage"}, "batteryMin": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Minimum battery level percentage"}, "batteryMinDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when minimum battery level occurred"}, "dateCom": {"type": "array", "items": {"type": "string"}, "description": "Array of communication dates"}, "nbrCom": {"type": "integer", "minimum": 0, "description": "Number of communications"}, "nbrUCoup": {"type": "integer", "minimum": 0, "description": "Number of power cuts"}, "timeBattery": {"type": "integer", "minimum": 0, "description": "Time on battery in seconds"}, "timeCom": {"type": "integer", "minimum": 0, "description": "Communication time in seconds"}}, "required": ["class", "batteryAvg", "batteryMin", "batteryMinDate", "dateCom", "nbrCom", "nbrUCoup", "timeBattery", "timeCom"]}]}, "StatBoot": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatBoot"]}, "bootCount": {"type": "integer", "minimum": 0, "description": "Number of system boots"}, "lastBootCause": {"type": ["integer", "null"], "description": "Cause code of the last boot"}, "lastBootDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of the last boot"}}, "required": ["class", "bootCount", "lastBootCause", "lastBootDate"]}]}, "StatCPU": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatCPU"]}, "cpuAvg": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Average CPU usage percentage"}, "cpuMax": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Maximum CPU usage percentage"}, "cpuMaxDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when maximum CPU usage occurred"}}, "required": ["class", "cpuAvg", "cpuMax", "cpuMaxDate"]}]}, "StatCellNeighbour": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatCellNeighbour"]}, "arfcn": {"type": "integer", "minimum": 0, "description": "Absolute Radio Frequency Channel Number"}, "ci": {"type": "integer", "minimum": 0, "description": "Cell Identity"}, "dateMes": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of measurement"}, "ecno": {"type": "integer", "description": "Energy per chip to noise power spectral density ratio"}, "lac": {"type": "integer", "minimum": 0, "description": "Location Area Code"}, "mcc": {"type": "integer", "minimum": 0, "description": "Mobile Country Code"}, "mnc": {"type": "integer", "minimum": 0, "description": "Mobile Network Code"}, "rscp": {"type": "integer", "description": "Received Signal Code Power"}, "rsrp": {"type": "integer", "description": "Reference Signal Received Power"}, "rsrq": {"type": "integer", "description": "Reference Signal Received Quality"}, "rssi": {"type": "integer", "description": "Received Signal Strength Indicator"}, "rxLevel": {"type": "integer", "description": "Received signal level"}, "techno": {"type": "string", "enum": ["2G", "3G", "4G", "5G", "LTE-M", "NB-IoT", "GPRS"], "description": "Technology type"}}, "required": ["class", "arfcn", "ci", "dateMes", "ecno", "lac", "mcc", "mnc", "rscp", "rsrp", "rsrq", "rssi", "rxLevel", "techno"]}]}, "StatCellServing": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatCellServing"]}, "arfcn": {"type": "integer", "minimum": 0, "description": "Absolute Radio Frequency Channel Number"}, "ber": {"type": "integer", "minimum": 0, "description": "Bit Error Rate"}, "ci": {"type": "integer", "minimum": 0, "description": "Cell Identity"}, "dateMes": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of measurement"}, "ecno": {"type": "integer", "description": "Energy per chip to noise power spectral density ratio"}, "lac": {"type": "integer", "minimum": 0, "description": "Location Area Code"}, "mcc": {"type": "integer", "minimum": 0, "description": "Mobile Country Code"}, "mnc": {"type": "integer", "minimum": 0, "description": "Mobile Network Code"}, "rscp": {"type": "integer", "description": "Received Signal Code Power"}, "rsrp": {"type": "integer", "description": "Reference Signal Received Power"}, "rsrq": {"type": "integer", "description": "Reference Signal Received Quality"}, "rssi": {"type": "integer", "description": "Received Signal Strength Indicator"}, "rxLevel": {"type": "integer", "description": "Received signal level"}, "rxQual": {"type": "integer", "description": "Received signal quality"}, "sRxLevel": {"type": "integer", "description": "Serving cell received signal level"}, "ta": {"type": "integer", "description": "Timing Advance"}, "techno": {"type": "string", "enum": ["2G", "3G", "4G", "5G", "LTE-M", "NB-IoT", "GPRS"], "description": "Technology type"}}, "required": ["class", "arfcn", "ber", "ci", "dateMes", "ecno", "lac", "mcc", "mnc", "rscp", "rsrp", "rsrq", "rssi", "rxLevel", "rxQual", "sRxLevel", "ta", "techno"]}]}, "StatClock": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatClock"]}, "last": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of last clock synchronization"}, "max": {"type": "integer", "minimum": 0, "description": "Maximum clock drift"}}, "required": ["class", "last", "max"]}]}, "StatDoor": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatDoor"]}, "lastOpenDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when door was last opened"}, "openCount": {"type": "integer", "minimum": 0, "description": "Number of times door was opened"}}, "required": ["class", "lastOpenDate", "openCount"]}]}, "StatEthernet": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatEthernet"]}, "nbOctets": {"type": "integer", "minimum": 0, "description": "Total number of octets"}, "nbOctetsCourant": {"type": "integer", "minimum": 0, "description": "Current number of octets"}, "rxHttpAPI": {"type": "integer", "minimum": 0, "description": "Received HTTP API bytes"}, "rxHttpDownload": {"type": "integer", "minimum": 0, "description": "Received HTTP download bytes"}, "rxHttpWeb": {"type": "integer", "minimum": 0, "description": "Received HTTP web bytes"}, "rxNtp": {"type": "integer", "minimum": 0, "description": "Received NTP bytes"}, "rxPing": {"type": "integer", "minimum": 0, "description": "Received ping bytes"}, "rxSsh": {"type": "integer", "minimum": 0, "description": "Received SSH bytes"}, "rxTotal": {"type": "integer", "minimum": 0, "description": "Total received bytes"}, "rxTotalLan": {"type": "integer", "minimum": 0, "description": "Total received LAN bytes"}, "txHttpAPI": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP API bytes"}, "txHttpDownload": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP download bytes"}, "txHttpWeb": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP web bytes"}, "txNtp": {"type": "integer", "minimum": 0, "description": "Transmitted NTP bytes"}, "txPing": {"type": "integer", "minimum": 0, "description": "Transmitted ping bytes"}, "txSsh": {"type": "integer", "minimum": 0, "description": "Transmitted SSH bytes"}, "txTotal": {"type": "integer", "minimum": 0, "description": "Total transmitted bytes"}, "txTotalLan": {"type": "integer", "minimum": 0, "description": "Total transmitted LAN bytes"}}, "required": ["class", "nbOctets", "nbOctetsCourant", "rxHttpAPI", "rxHttpDownload", "rxHttpWeb", "rxNtp", "rxPing", "rxSsh", "rxTotal", "rxTotalLan", "txHttpAPI", "txHttpDownload", "txHttpWeb", "txNtp", "txPing", "txSsh", "txTotal", "txTotalLan"]}]}, "StatFS": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatFS"]}, "available": {"type": "integer", "minimum": 0, "description": "Available disk space in bytes"}, "capacity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Disk usage percentage"}, "partition": {"type": "string", "description": "Partition name"}, "used": {"type": "integer", "minimum": 0, "description": "Used disk space in bytes"}}, "required": ["class", "available", "capacity", "partition", "used"]}]}, "StatFlash": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatFlash"]}, "blocksKO": {"type": "integer", "minimum": 0, "description": "Number of failed blocks"}, "nbBlocks": {"type": "integer", "minimum": 0, "description": "Total number of blocks"}}, "required": ["class", "blocksKO", "nbBlocks"]}]}, "StatLog": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatLog"]}, "logName": {"type": "array", "items": {"type": "string"}, "description": "Array of log names"}}, "required": ["class", "logName"]}]}, "StatMeter": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatMeter"]}, "lastMissingDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of last missing meter reading"}, "missingCount": {"type": "integer", "minimum": 0, "description": "Number of missing meter readings"}}, "required": ["class", "lastMissingDate", "missingCount"]}]}, "StatNetwork": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatNetwork"]}, "logs": {"type": "array", "items": {"type": "string"}, "description": "Array of network log entries"}, "nbAppels": {"type": "integer", "minimum": 0, "description": "Number of calls"}, "nbAppelsCourant": {"type": "integer", "minimum": 0, "description": "Current number of calls"}, "nbSMSrx": {"type": "integer", "minimum": 0, "description": "Number of SMS received"}, "nbSMSrxCourant": {"type": "integer", "minimum": 0, "description": "Current number of SMS received"}, "nbSMStx": {"type": "integer", "minimum": 0, "description": "Number of SMS transmitted"}, "nbSMStxCourant": {"type": "integer", "minimum": 0, "description": "Current number of SMS transmitted"}, "selectKO": {"type": "integer", "minimum": 0, "description": "Number of failed selections"}}, "required": ["class", "logs", "nb<PERSON><PERSON><PERSON>", "nbAppelsCourant", "nbSMSrx", "nbSMSrxCourant", "nbSMStx", "nbSMStxCourant", "selectKO"]}]}, "StatRAM": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatRAM"]}, "ramAvg": {"type": "integer", "minimum": 0, "description": "Average RAM usage percentage"}, "ramMax": {"type": "integer", "minimum": 0, "description": "Maximum RAM usage percentage"}, "ramMaxDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp when maximum RAM usage occurred"}}, "required": ["class", "ramAvg", "ramMax", "ramMaxDate"]}]}, "StatSIM": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatSIM"]}, "detectFailure": {"type": "integer", "minimum": 0, "description": "Number of SIM detection failures"}, "readFailure": {"type": "integer", "minimum": 0, "description": "Number of SIM read failures"}}, "required": ["class", "detectFailure", "readFailure"]}]}, "StatSecurity": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatSecurity"]}, "callAuthKO": {"type": "integer", "minimum": 0, "description": "Number of failed call authentications"}, "callNum": {"type": "string", "description": "Call number"}, "callNumNb": {"type": "integer", "minimum": 0, "description": "Number of call numbers"}, "radiusAuthKO": {"type": "integer", "minimum": 0, "description": "Number of failed RADIUS authentications"}, "smsAuthKO": {"type": "integer", "minimum": 0, "description": "Number of failed SMS authentications"}, "smsNum": {"type": "string", "description": "SMS number"}, "smsNumNb": {"type": "integer", "minimum": 0, "description": "Number of SMS numbers"}, "tlsAuthKO": {"type": "integer", "minimum": 0, "description": "Number of failed TLS authentications"}}, "required": ["class", "callAuthKO", "callNum", "callNumNb", "radiusAuthKO", "smsAuthKO", "smsNum", "smsNumNb", "tlsAuthKO"]}]}, "StatSupply": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatSupply"]}, "lastLossDate": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$", "description": "ISO 8601 timestamp of last power loss"}, "lossCount": {"type": "integer", "minimum": 0, "description": "Number of power losses"}}, "required": ["class", "lastLossDate", "lossCount"]}]}, "StatWAN": {"allOf": [{"$ref": "#/definitions/StatObjectWithDateDuration"}, {"type": "object", "properties": {"class": {"type": "string", "enum": ["StatWAN"]}, "bpsRxAv": {"type": "integer", "minimum": 0, "description": "Average received bits per second"}, "bpsRxMax": {"type": "integer", "minimum": 0, "description": "Maximum received bits per second"}, "bpsRxMin": {"type": "integer", "minimum": 0, "description": "Minimum received bits per second"}, "bpsTxAv": {"type": "integer", "minimum": 0, "description": "Average transmitted bits per second"}, "bpsTxMax": {"type": "integer", "minimum": 0, "description": "Maximum transmitted bits per second"}, "bpsTxMin": {"type": "integer", "minimum": 0, "description": "Minimum transmitted bits per second"}, "httpTmo": {"type": "integer", "minimum": 0, "description": "Number of HTTP timeouts"}, "logs": {"type": "array", "items": {"type": "string"}, "description": "Array of WAN log entries"}, "nbOctets": {"type": "integer", "minimum": 0, "description": "Total number of octets"}, "nbOctetsCourant": {"type": "integer", "minimum": 0, "description": "Current number of octets"}, "pdpKO": {"type": "integer", "minimum": 0, "description": "Number of failed PDP contexts"}, "pdpOK": {"type": "integer", "minimum": 0, "description": "Number of successful PDP contexts"}, "rxHttpAPI": {"type": "integer", "minimum": 0, "description": "Received HTTP API bytes"}, "rxHttpDownload": {"type": "integer", "minimum": 0, "description": "Received HTTP download bytes"}, "rxHttpWeb": {"type": "integer", "minimum": 0, "description": "Received HTTP web bytes"}, "rxNtp": {"type": "integer", "minimum": 0, "description": "Received NTP bytes"}, "rxPing": {"type": "integer", "minimum": 0, "description": "Received ping bytes"}, "rxSsh": {"type": "integer", "minimum": 0, "description": "Received SSH bytes"}, "rxTotal": {"type": "integer", "minimum": 0, "description": "Total received bytes"}, "tcpCnxKO": {"type": "integer", "minimum": 0, "description": "Number of failed TCP connections"}, "tcpCnxOK": {"type": "integer", "minimum": 0, "description": "Number of successful TCP connections"}, "tcpDisconnect": {"type": "integer", "minimum": 0, "description": "Number of TCP disconnections"}, "tcpTmo": {"type": "integer", "minimum": 0, "description": "Number of TCP timeouts"}, "txHttpAPI": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP API bytes"}, "txHttpDownload": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP download bytes"}, "txHttpWeb": {"type": "integer", "minimum": 0, "description": "Transmitted HTTP web bytes"}, "txNtp": {"type": "integer", "minimum": 0, "description": "Transmitted NTP bytes"}, "txPing": {"type": "integer", "minimum": 0, "description": "Transmitted ping bytes"}, "txSsh": {"type": "integer", "minimum": 0, "description": "Transmitted SSH bytes"}, "txTotal": {"type": "integer", "minimum": 0, "description": "Total transmitted bytes"}}, "required": ["class", "bpsRxAv", "bpsRxMax", "bpsRxMin", "bpsTxAv", "bpsTxMax", "bpsTxMin", "httpTmo", "logs", "nbOctets", "nbOctetsCourant", "pdpKO", "pdpOK", "rxHttpAPI", "rxHttpDownload", "rxHttpWeb", "rxNtp", "rxPing", "rxSsh", "rxTotal", "tcpCnxKO", "tcpCnxOK", "tcpDisconnect", "tcpTmo", "txHttpAPI", "txHttpDownload", "txHttpWeb", "txNtp", "txPing", "txSsh", "txTotal"]}]}}}